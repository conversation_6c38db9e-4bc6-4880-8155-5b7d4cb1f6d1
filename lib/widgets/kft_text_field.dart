import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/kft_design_system.dart';
import '../utils/phone_utils.dart';

/// KFT Text Field
/// A custom text field widget that follows the KFT design system
/// Supports various input types and validation

enum KFTTextFieldType {
  text,
  email,
  password,
  phone,
  number,
  multiline,
}

class KFTTextField extends StatefulWidget {
  final String label;
  final String? hint;
  final String? errorText;
  final String? helperText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final KFTTextFieldType type;
  final bool isEnabled;
  final bool isRequired;
  final bool autofocus;
  final int? maxLength;
  final int? maxLines;
  final TextInputAction? textInputAction;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final Function()? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? prefix;
  final Widget? suffix;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;

  const KFTTextField({
    Key? key,
    required this.label,
    this.hint,
    this.errorText,
    this.helperText,
    this.controller,
    this.focusNode,
    this.type = KFTTextFieldType.text,
    this.isEnabled = true,
    this.isRequired = false,
    this.autofocus = false,
    this.maxLength,
    this.maxLines,
    this.textInputAction,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.inputFormatters,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
  }) : super(key: key);

  @override
  State<KFTTextField> createState() => _KFTTextFieldState();
}

class _KFTTextFieldState extends State<KFTTextField> {
  bool _obscureText = true;
  String? _phoneValidationMessage;

  @override
  Widget build(BuildContext context) {
    // Determine keyboard type based on field type
    final TextInputType keyboardType = _getKeyboardType();

    // Determine input formatters
    final List<TextInputFormatter> formatters = _getInputFormatters();

    // Determine max lines
    final int maxLines = widget.type == KFTTextFieldType.multiline
        ? widget.maxLines ?? 5
        : widget.type == KFTTextFieldType.password ? 1 : widget.maxLines ?? 1;

    // Build suffix icon
    Widget? suffixIcon = _buildSuffixIcon();

    return TextField(
      controller: widget.controller,
      focusNode: widget.focusNode,
      keyboardType: keyboardType,
      textInputAction: widget.textInputAction,
      obscureText: widget.type == KFTTextFieldType.password && _obscureText,
      enabled: widget.isEnabled,
      autofocus: widget.autofocus,
      maxLength: widget.maxLength,
      maxLines: maxLines,
      minLines: widget.type == KFTTextFieldType.multiline ? 3 : 1,
      onChanged: (value) {
        if (widget.type == KFTTextFieldType.phone) {
          _validatePhoneNumber(value);
        }
        widget.onChanged?.call(value);
      },
      onSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      inputFormatters: [...formatters, ...?widget.inputFormatters],
      style: KFTDesignSystem.bodyMedium,
      cursorColor: KFTDesignSystem.primaryColor,
      decoration: InputDecoration(
        labelText: widget.label + (widget.isRequired ? ' *' : ''),
        hintText: widget.hint ?? _getDefaultHint(),
        errorText: widget.errorText,
        helperText: widget.type == KFTTextFieldType.phone ? _phoneValidationMessage : widget.helperText,
        helperText: widget.helperText,
        helperStyle: KFTDesignSystem.bodySmall.copyWith(
          color: KFTDesignSystem.textSecondaryColor,
        ),
        errorStyle: KFTDesignSystem.bodySmall.copyWith(
          color: KFTDesignSystem.errorColor,
        ),
        labelStyle: KFTDesignSystem.bodyMedium.copyWith(
          color: KFTDesignSystem.textSecondaryColor,
          fontWeight: KFTDesignSystem.fontWeightMedium,
        ),
        floatingLabelStyle: KFTDesignSystem.bodySmall.copyWith(
          color: widget.errorText != null
              ? KFTDesignSystem.errorColor
              : KFTDesignSystem.primaryColor,
          fontWeight: KFTDesignSystem.fontWeightMedium,
        ),
        hintStyle: KFTDesignSystem.bodyMedium.copyWith(
          color: KFTDesignSystem.textTertiaryColor,
        ),
        prefixIcon: widget.prefixIcon != null
            ? Icon(widget.prefixIcon,
                color: widget.errorText != null
                    ? KFTDesignSystem.errorColor
                    : KFTDesignSystem.textSecondaryColor,
                size: 20,
              )
            : null,
        prefix: widget.prefix,
        suffix: widget.suffix,
        suffixIcon: suffixIcon,
        filled: true,
        fillColor: widget.isEnabled
            ? KFTDesignSystem.surfaceColor
            : KFTDesignSystem.dividerColor.withOpacity(0.3),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: KFTDesignSystem.spacingMd,
          vertical: KFTDesignSystem.spacingMd,
        ),
        isDense: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
          borderSide: BorderSide(
            color: KFTDesignSystem.borderColor,
            width: KFTDesignSystem.borderWidthRegular,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
          borderSide: BorderSide(
            color: KFTDesignSystem.borderColor,
            width: KFTDesignSystem.borderWidthRegular,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
          borderSide: BorderSide(
            color: KFTDesignSystem.primaryColor,
            width: KFTDesignSystem.borderWidthThick,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
          borderSide: BorderSide(
            color: KFTDesignSystem.errorColor,
            width: KFTDesignSystem.borderWidthRegular,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
          borderSide: BorderSide(
            color: KFTDesignSystem.errorColor,
            width: KFTDesignSystem.borderWidthThick,
          ),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
          borderSide: BorderSide(color: KFTDesignSystem.borderColor.withOpacity(0.5), width: 1),
        ),
      ),
    );
  }

  TextInputType _getKeyboardType() {
    switch (widget.type) {
      case KFTTextFieldType.email:
        return TextInputType.emailAddress;
      case KFTTextFieldType.password:
        return TextInputType.visiblePassword;
      case KFTTextFieldType.phone:
        return TextInputType.phone;
      case KFTTextFieldType.number:
        return TextInputType.number;
      case KFTTextFieldType.multiline:
        return TextInputType.multiline;
      case KFTTextFieldType.text:
      default:
        return TextInputType.text;
    }
  }

  List<TextInputFormatter> _getInputFormatters() {
    switch (widget.type) {
      case KFTTextFieldType.phone:
        // Allow optional leading + and digits, with better formatting
        return [
          FilteringTextInputFormatter.allow(RegExp(r'[\+\d\s\-\(\)]')),
          LengthLimitingTextInputFormatter(20), // Reasonable max length
        ];
      case KFTTextFieldType.number:
        return [FilteringTextInputFormatter.digitsOnly];
      case KFTTextFieldType.email:
      case KFTTextFieldType.password:
      case KFTTextFieldType.text:
      case KFTTextFieldType.multiline:
      default:
        return [];
    }
  }

  Widget? _buildSuffixIcon() {
    // For password fields, show toggle visibility icon
    if (widget.type == KFTTextFieldType.password) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_outlined : Icons.visibility_off_outlined,
          color: KFTDesignSystem.textSecondaryColor,
        ),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    }

    // For custom suffix icon
    if (widget.suffixIcon != null) {
      return IconButton(
        icon: Icon(
          widget.suffixIcon,
          color: KFTDesignSystem.textSecondaryColor,
        ),
        onPressed: widget.onSuffixIconPressed,
      );
    }

    return null;
  }

  String? _getDefaultHint() {
    switch (widget.type) {
      case KFTTextFieldType.phone:
        return 'e.g., +91XXXXXXXXXX or XXXXXXXXXX';
      case KFTTextFieldType.email:
        return 'Enter your email address';
      default:
        return null;
    }
  }

  void _validatePhoneNumber(String value) {
    if (value.isEmpty) {
      setState(() {
        _phoneValidationMessage = null;
      });
      return;
    }

    final validationResult = PhoneUtils.validatePhoneNumber(value);
    setState(() {
      if (validationResult.isValid) {
        _phoneValidationMessage = PhoneUtils.getValidationHint(value);
      } else {
        _phoneValidationMessage = validationResult.errorMessage;
      }
    });
  }
}
